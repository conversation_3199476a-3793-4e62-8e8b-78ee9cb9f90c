﻿using MvCameraControl;
using System.Text;
using System.Diagnostics;
using GGEC.AOI.T68ZJ.Log;

namespace GGEC.AOI.T68ZJ.Managers
{
    public class CameraManager
    {
        List<IDeviceInfo> deviceInfos = new List<IDeviceInfo>();
        List<IDevice> devices = new List<IDevice>();

        // 同步图像缓存 - 确保两个相机图像同时拍摄
        private readonly object syncImageLock = new object();
        private IImage? cachedImage1;
        private IImage? cachedImage2;
        private DateTime lastSyncCacheTime = DateTime.MinValue;

        // 同步采集线程管理
        private Thread? syncGrabThread;
        private bool syncGrabThreadRunning = false;
        private readonly object threadLock = new object();

        // 预览线程管理
        private Thread? previewThread;
        private bool previewThreadRunning = false;
        private readonly object previewLock = new object();
        private CancellationTokenSource? previewCancellationTokenSource;
        private PictureBox? previewPictureBox1;
        private PictureBox? previewPictureBox2;

        private static CameraManager? _instance;

        // 相机参数配置
        public double ExposureTime { get; set; } = 2000.0; // 曝光时间（微秒）
        public double Gain { get; set; } = 20.0; // 增益值
        public int MinPhotoIntervalMs { get; set; } = 200; // 最小拍照间隔（毫秒）
        public string PixelFormat { get; set; } = "Mono8"; // 像素格式

        // 兼容性属性
        public double ConveyorBeltSpeed { get; set; } = 0.3; // 传送带速度（米/秒）
        public int SensorTriggerDelayCompensation { get; set; } = 50; // 传感器触发延迟补偿（毫秒）
        public bool AutoExposureEnabled { get; set; } = false; // 是否启用自动曝光
        public bool AutoGainEnabled { get; set; } = false; // 是否启用自动增益
        public bool ShowLivePreview { get; set; } = false; // 预览状态

        // 拍照质量控制
        private readonly object photoQualityLock = new object();
        private DateTime lastPhotoTime = DateTime.MinValue;

        // 私有构造函数，防止外部实例化
        private CameraManager()
        {
            Logger.Info("CameraManager初始化完成");
        }

        public static CameraManager Instance
        {
            get
            {
                _instance ??= new CameraManager();
                return _instance;
            }
        }

        public void InitCameras()
        {
            Logger.Info("开始初始化相机");

            try
            {
                // 清理之前的设备
                Dispose();

                int nRet = DeviceEnumerator.EnumDevices(DeviceTLayerType.MvGigEDevice, out deviceInfos);
                if (nRet != 0)
                {
                    string errorMsg = GetErrorMessage(nRet);
                    Logger.Error($"枚举设备失败，错误码：{nRet:X8}，错误信息：{errorMsg}");
                    throw new Exception($"枚举设备失败：{errorMsg}");
                }

                if (deviceInfos.Count == 0)
                {
                    Logger.Warn("未找到任何GigE设备");
                    throw new Exception("未找到任何GigE设备，请检查设备连接和网络配置");
                }

                Logger.Info($"找到 {deviceInfos.Count} 个设备");

                for (int i = 0; i < deviceInfos.Count; i++)
                {
                    var deviceInfo = deviceInfos[i];
                    Logger.Info($"正在初始化设备 {i + 1}: {deviceInfo.SerialNumber}");

                    try
                    {
                        IDevice device = DeviceFactory.CreateDevice(deviceInfo);
                        if (device == null)
                        {
                            throw new Exception($"创建设备失败：{deviceInfo.SerialNumber}");
                        }

                        nRet = device.Open();
                        if (nRet != 0)
                        {
                            string errorMsg = GetErrorMessage(nRet);
                            device?.Dispose();
                            throw new Exception($"打开设备失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                        }

                        // 配置设备参数
                        ConfigureDevice(device, deviceInfo);

                        // 开始采集
                        //device.StreamGrabber.SetOutputQueueSize(3);
                        //device.StreamGrabber.SetImageNodeNum(5);
                        nRet = device.StreamGrabber.StartGrabbing();
                        if (nRet != 0)
                        {
                            string errorMsg = GetErrorMessage(nRet);
                            device.Close();
                            device.Dispose();
                            throw new Exception($"开始采集失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                        }

                        devices.Add(device);

                        Logger.Info($"设备 {deviceInfo.SerialNumber} 初始化成功");
                    }
                    catch (Exception ex)
                    {
                        Logger.Exception(ex, $"初始化设备 {i + 1} 时发生异常");

                        // 清理已初始化的设备
                        StopSynchronizedCacheThread();
                        foreach (var dev in devices)
                        {
                            try
                            {
                                dev.StreamGrabber.StopGrabbing();
                                dev.Close();
                                dev.Dispose();
                            }
                            catch (Exception cleanupEx)
                            {
                                Logger.Exception(cleanupEx, "清理设备时发生异常");
                            }
                        }
                        devices.Clear();
                        throw;
                    }
                }

                // 启动同步图像缓存线程
                if (devices.Count >= 2)
                {
                    StartSynchronizedImageCacheThread();
                }

                Logger.Info($"相机初始化完成，成功初始化 {devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "相机初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 启动同步图像缓存线程 - 确保两个相机同时拍摄
        /// </summary>
        private void StartSynchronizedImageCacheThread()
        {
            lock (threadLock)
            {
                // 如果线程已经在运行，先停止
                if (syncGrabThread != null && syncGrabThread.IsAlive)
                {
                    syncGrabThreadRunning = false;
                    syncGrabThread.Join(1000);
                }

                // 启动新的同步缓存线程
                syncGrabThreadRunning = true;
                syncGrabThread = new Thread(SynchronizedImageCacheWorker)
                {
                    Name = "SyncImageCache",
                    IsBackground = true
                };
                syncGrabThread.Start();

                Logger.Info("同步图像缓存线程已启动");
            }
        }

        /// <summary>
        /// 同步图像缓存线程工作函数 - 同时从两个相机获取图像，确保时间同步
        /// </summary>
        private void SynchronizedImageCacheWorker()
        {
            Logger.Info("同步图像缓存线程开始运行");

            while (syncGrabThreadRunning)
            {
                try
                {
                    // 同时从两个相机获取图像
                    var captureTime = DateTime.Now;
                    var image1 = CaptureImageFromDevice(0);
                    var image2 = CaptureImageFromDevice(1);

                    // 只有当两个图像都成功获取时才更新缓存
                    if (image1 != null && image2 != null)
                    {
                        UpdateSynchronizedImageCache(image1, image2, captureTime);
                    }
                    else
                    {
                        // 释放部分获取的图像
                        image1?.Dispose();
                        image2?.Dispose();
                    }

                    // 控制缓存更新频率，约15fps确保实时性
                    Thread.Sleep(66);
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, "同步图像缓存线程发生异常");
                    Thread.Sleep(500);
                }
            }

            Logger.Info("同步图像缓存线程已停止");
        }

        /// <summary>
        /// 更新同步图像缓存 - 同时保存两个相机的图像，确保时间同步
        /// </summary>
        private void UpdateSynchronizedImageCache(IImage image1, IImage image2, DateTime captureTime)
        {
            lock (syncImageLock)
            {
                // 释放旧的缓存图像
                cachedImage1?.Dispose();
                cachedImage2?.Dispose();

                // 保存新的同步图像
                cachedImage1 = image1;
                cachedImage2 = image2;
                lastSyncCacheTime = captureTime;

                Logger.Debug($"同步图像缓存已更新，拍摄时间：{captureTime:HH:mm:ss.fff}");
            }
        }

        /// <summary>
        /// 停止同步图像缓存线程
        /// </summary>
        private void StopSynchronizedCacheThread()
        {
            lock (threadLock)
            {
                Logger.Info("停止同步图像缓存线程");

                // 设置停止标志
                syncGrabThreadRunning = false;

                // 等待线程结束
                if (syncGrabThread != null && syncGrabThread.IsAlive)
                {
                    try
                    {
                        syncGrabThread.Join(2000);
                        if (syncGrabThread.IsAlive)
                        {
                            Logger.Warn("同步缓存线程未能在2秒内停止");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Exception(ex, "停止同步缓存线程时发生异常");
                    }
                }

                syncGrabThread = null;
            }
        }

        /// <summary>
        /// 配置设备参数 - 针对传送带AOI检测优化
        /// </summary>
        private void ConfigureDevice(IDevice device, IDeviceInfo deviceInfo)
        {
            try
            {
                Logger.Info($"开始配置设备参数：{deviceInfo.SerialNumber}");

                // 如果是GigE设备，优化网络参数
                if (device is IGigEDevice gigEDevice)
                {
                    Logger.Info($"配置GigE设备网络参数：{deviceInfo.SerialNumber}");

                    // 获取最佳包大小
                    int optimalPacketSize;
                    int result = gigEDevice.GetOptimalPacketSize(out optimalPacketSize);
                    if (result == 0)
                    {
                        result = device.Parameters.SetIntValue("GevSCPSPacketSize", optimalPacketSize);
                        if (result == 0)
                        {
                            Logger.Info($"设置包大小为：{optimalPacketSize}");
                        }
                        else
                        {
                            Logger.Warn($"设置包大小失败，错误码：{result:X8}");
                        }
                    }
                    else
                    {
                        Logger.Warn($"获取最佳包大小失败，错误码：{result:X8}");
                    }
                }

                // 设置图像格式和尺寸
                ConfigureImageFormat(device);

                // 设置采集模式
                ConfigureAcquisitionMode(device);

                // 设置曝光和增益参数（针对传送带优化）
                ConfigureExposureAndGain(device);

                // 设置触发模式
                ConfigureTriggerMode(device);

                Logger.Info($"设备参数配置完成：{deviceInfo.SerialNumber}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"配置设备参数时发生异常：{deviceInfo.SerialNumber}");
                // 配置失败不抛出异常，使用默认参数
            }
        }

        /// <summary>
        /// 配置图像格式和尺寸
        /// </summary>
        private void ConfigureImageFormat(IDevice device)
        {
            try
            {
                // 设置像素格式（使用变量中的设置）
                var pixelFormatResult = device.Parameters.SetEnumValueByString("PixelFormat", PixelFormat);
                if (pixelFormatResult == 0)
                {
                    Logger.Info($"设置像素格式为{PixelFormat}");
                }
                else
                {
                    Logger.Warn($"设置像素格式失败，错误码：{pixelFormatResult:X8}，尝试使用默认格式");
                    // 如果设置失败，尝试常见的格式
                    var fallbackFormats = new[] { "Mono8", "RGB8", "BGR8", "BayerRG8" };
                    foreach (var format in fallbackFormats)
                    {
                        var fallbackResult = device.Parameters.SetEnumValueByString("PixelFormat", format);
                        if (fallbackResult == 0)
                        {
                            Logger.Info($"使用备用像素格式：{format}");
                            PixelFormat = format; // 更新变量
                            break;
                        }
                    }
                }

                // 获取并记录当前图像尺寸
                device.Parameters.GetIntValue("Width", out IIntValue width);
                device.Parameters.GetIntValue("Height", out IIntValue height);
                Logger.Info($"当前图像尺寸: {width?.CurValue} x {height?.CurValue}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置图像格式时发生异常");
            }
        }

        /// <summary>
        /// 配置采集模式
        /// </summary>
        private void ConfigureAcquisitionMode(IDevice device)
        {
            try
            {
                // 设置连续采集模式
                var result = device.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                if (result == 0)
                {
                    Logger.Info("设置为连续采集模式");
                }
                else
                {
                    Logger.Warn($"设置连续采集模式失败，错误码：{result:X8}");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置采集模式时发生异常");
            }
        }

        /// <summary>
        /// 配置曝光时间和增益 - 使用变量参数
        /// </summary>
        private void ConfigureExposureAndGain(IDevice device)
        {
            try
            {
                // 设置自动曝光模式
                if (AutoExposureEnabled)
                {
                    var autoExposureResult = device.Parameters.SetEnumValueByString("ExposureAuto", "Continuous");
                    if (autoExposureResult == 0)
                    {
                        Logger.Info("启用自动曝光模式");
                    }
                    else
                    {
                        Logger.Warn($"启用自动曝光失败，错误码：{autoExposureResult:X8}");
                    }
                }
                else
                {
                    // 关闭自动曝光，使用手动设置
                    device.Parameters.SetEnumValueByString("ExposureAuto", "Off");

                    // 设置曝光时间
                    var exposureResult = device.Parameters.SetFloatValue("ExposureTime", (float)ExposureTime);
                    if (exposureResult == 0)
                    {
                        Logger.Info($"设置曝光时间为{ExposureTime}微秒（适合传送带运动）");
                    }
                    else
                    {
                        Logger.Warn($"设置曝光时间失败，错误码：{exposureResult:X8}");
                    }
                }

                // 设置自动增益模式
                if (AutoGainEnabled)
                {
                    var autoGainResult = device.Parameters.SetEnumValueByString("GainAuto", "Continuous");
                    if (autoGainResult == 0)
                    {
                        Logger.Info("启用自动增益模式");
                    }
                    else
                    {
                        Logger.Warn($"启用自动增益失败，错误码：{autoGainResult:X8}");
                    }
                }
                else
                {
                    // 关闭自动增益，使用手动设置
                    device.Parameters.SetEnumValueByString("GainAuto", "Off");

                    // 设置增益值
                    var gainResult = device.Parameters.SetFloatValue("Gain", (float)Gain);
                    if (gainResult == 0)
                    {
                        Logger.Info($"设置增益为{Gain}");
                    }
                    else
                    {
                        Logger.Warn($"设置增益失败，错误码：{gainResult:X8}");
                    }
                }

                // 记录当前曝光和增益设置
                device.Parameters.GetFloatValue("ExposureTime", out IFloatValue exposureTime);
                device.Parameters.GetFloatValue("Gain", out IFloatValue gain);
                Logger.Info($"当前曝光时间: {exposureTime?.CurValue}μs, 增益: {gain?.CurValue}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置曝光和增益时发生异常");
            }
        }

        /// <summary>
        /// 配置触发模式
        /// </summary>
        private void ConfigureTriggerMode(IDevice device)
        {
            try
            {
                // 设置触发模式为关闭（连续采集）
                var result = device.Parameters.SetEnumValueByString("TriggerMode", "Off");
                if (result == 0)
                {
                    Logger.Info("关闭触发模式（连续采集）");
                }
                else
                {
                    Logger.Warn($"关闭触发模式失败，错误码：{result:X8}");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置触发模式时发生异常");
            }
        }













        /// <summary>
        /// 安全地更新PictureBox图像（处理UI线程调用）
        /// </summary>
        private void UpdatePictureBoxSafely(PictureBox pictureBox, IImage image)
        {
            if (pictureBox == null || image == null) return;

            try
            {
                var bitmap = image.ToBitmap();
                if (bitmap == null) return;

                if (pictureBox.InvokeRequired)
                {
                    pictureBox.Invoke(new Action(() =>
                    {
                        var oldImage = pictureBox.Image;
                        pictureBox.Image = bitmap;
                        oldImage?.Dispose();
                    }));
                }
                else
                {
                    var oldImage = pictureBox.Image;
                    pictureBox.Image = bitmap;
                    oldImage?.Dispose();
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "更新PictureBox图像时发生异常");
            }
        }

        /// <summary>
        /// 传感器触发的拍照 - 使用同步预缓存的图像，确保两个相机图像完全同步
        /// </summary>
        public (IImage? image1, IImage? image2) TakeSensorTriggeredPhotos()
        {
            lock (photoQualityLock)
            {
                // 防止频繁触发
                var timeSinceLastPhoto = DateTime.Now - lastPhotoTime;
                if (timeSinceLastPhoto.TotalMilliseconds < MinPhotoIntervalMs)
                {
                    Logger.Warn($"拍照间隔过短（{timeSinceLastPhoto.TotalMilliseconds:F0}ms），忽略此次触发");
                    return (null, null);
                }
                lastPhotoTime = DateTime.Now;
            }

            if (devices.Count < 2)
            {
                Logger.Error($"设备数量不足，当前设备数：{devices.Count}，需要至少2个设备");
                return (null, null);
            }

            Logger.Info("传感器触发拍照 - 使用同步预缓存图像");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                IImage? image1 = null;
                IImage? image2 = null;

                // 获取同步预缓存的图像
                lock (syncImageLock)
                {
                    if (cachedImage1 != null && cachedImage2 != null)
                    {
                        // 检查图像是否太旧（超过500ms）
                        var imageAge = DateTime.Now - lastSyncCacheTime;
                        if (imageAge.TotalMilliseconds < 500)
                        {
                            image1 = cachedImage1;
                            image2 = cachedImage2;
                            Logger.Debug($"使用同步预缓存图像，图像年龄：{imageAge.TotalMilliseconds:F0}ms，" +
                                       $"拍摄时间：{lastSyncCacheTime:HH:mm:ss.fff}");
                        }
                        else
                        {
                            Logger.Warn($"同步缓存图像过旧（{imageAge.TotalMilliseconds:F0}ms），跳过");
                        }
                    }
                    else
                    {
                        Logger.Warn("无可用的同步缓存图像");
                    }
                }

                stopwatch.Stop();
                Logger.Info($"传感器触发拍照完成，耗时：{stopwatch.ElapsedMilliseconds}ms，" +
                           $"相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}");

                return (image1, image2);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Logger.Exception(ex, $"传感器触发拍照异常，耗时：{stopwatch.ElapsedMilliseconds}ms");
                return (null, null);
            }
        }







        /// <summary>
        /// 从相机硬件获取图像 - 用于图像缓存线程
        /// </summary>
        private IImage? CaptureImageFromDevice(int deviceIndex)
        {
            if (deviceIndex >= devices.Count)
            {
                return null;
            }

            Stopwatch sw = Stopwatch.StartNew();
            try
            {
                var device = devices[deviceIndex];
                int nRet = device.StreamGrabber.GetImageBuffer(1000, out IFrameOut frameOut);
                //ImageFormatInfo imageFormatInfo = new ImageFormatInfo();
                //imageFormatInfo.JpegQuality = 90; // 设置JPEG质量
                //imageFormatInfo.FormatType = ImageFormatType.Jpeg;
                //device.ImageSaver.SaveImageToFile($"output/{DateTime.Now:yyyyMMdd}/Image_{deviceIndex}_{DateTime.Now:yyyyMMdd_HHmmss}.jpg", frameOut.Image, imageFormatInfo, CFAMethod.Fast);
                //sw.Stop();
                //Logger.Info($"设备 {deviceIndex} 获取图像并保存耗时：{sw.ElapsedMilliseconds}ms");
                if (nRet == 0 && frameOut?.Image != null)
                {
                    var image = frameOut.Image;
                    frameOut.Dispose();
                    return image;
                }
                else
                {
                    frameOut?.Dispose();
                    return null;
                }
            }
            catch (Exception ex)
            {
                sw.Stop();
                Logger.Exception(ex, $"设备 {deviceIndex} 获取图像时发生异常，耗时：{sw.ElapsedMilliseconds}ms");
                return null;
            }
        }

        /// <summary>
        /// 兼容性方法 - 单个相机拍照
        /// </summary>
        public IImage? TakePhoto(int deviceIndex)
        {
            lock (syncImageLock)
            {
                if (deviceIndex == 0)
                {
                    return cachedImage1;
                }
                else if (deviceIndex == 1)
                {
                    return cachedImage2;
                }
                return null;
            }
        }

        /// <summary>
        /// 兼容性方法 - 异步传感器触发拍照
        /// </summary>
        public async Task<(IImage? image1, IImage? image2)> TakeSensorTriggeredPhotosAsync()
        {
            return await Task.Run(() => TakeSensorTriggeredPhotos());
        }

        /// <summary>
        /// 兼容性方法 - 自动调整传送带参数
        /// </summary>
        public void AutoAdjustForConveyorSpeed()
        {
            Logger.Info($"根据传送带速度 {ConveyorBeltSpeed}m/s 自动调整参数");
            // 简化实现，不做实际调整
        }

        /// <summary>
        /// 兼容性方法 - 记录相机参数
        /// </summary>
        public void LogCurrentCameraParameters(int deviceIndex)
        {
            Logger.Info($"设备 {deviceIndex} 当前参数：曝光时间={ExposureTime}μs, 增益={Gain}");
        }

        /// <summary>
        /// 兼容性方法 - 获取内存使用情况
        /// </summary>
        public string GetMemoryUsage()
        {
            long memory = GC.GetTotalMemory(false) / 1024 / 1024;
            return $"当前内存: {memory} MB";
        }

        /// <summary>
        /// 开始实时预览
        /// </summary>
        public void StartLivePreview(object pictureBox1, object pictureBox2)
        {
            lock (previewLock)
            {
                if (previewThreadRunning)
                {
                    Logger.Info("预览已在运行中");
                    return;
                }

                if (pictureBox1 is PictureBox pb1 && pictureBox2 is PictureBox pb2)
                {
                    previewPictureBox1 = pb1;
                    previewPictureBox2 = pb2;
                    ShowLivePreview = true;
                    previewThreadRunning = true;
                    previewCancellationTokenSource = new CancellationTokenSource();

                    previewThread = new Thread(() => PreviewWorker(previewCancellationTokenSource.Token))
                    {
                        Name = "PreviewThread",
                        IsBackground = true
                    };
                    previewThread.Start();

                    Logger.Info("实时预览已启动");
                }
                else
                {
                    Logger.Error("预览启动失败：PictureBox参数无效");
                }
            }
        }

        /// <summary>
        /// 停止实时预览
        /// </summary>
        public void StopLivePreview()
        {
            lock (previewLock)
            {
                if (!previewThreadRunning)
                {
                    Logger.Info("预览未在运行");
                    return;
                }

                ShowLivePreview = false;
                previewThreadRunning = false;
                previewCancellationTokenSource?.Cancel();

                if (previewThread != null && previewThread.IsAlive)
                {
                    try
                    {
                        // 检查线程状态是否有效
                        if (previewThread.ThreadState != System.Threading.ThreadState.Aborted &&
                            previewThread.ThreadState != System.Threading.ThreadState.Stopped)
                        {
                            previewThread.Join(1000);
                            if (previewThread.IsAlive)
                            {
                                Logger.Warn("预览线程未能在1秒内停止");
                            }
                        }
                        else
                        {
                            Logger.Info($"预览线程已处于状态: {previewThread.ThreadState}");
                        }
                    }
                    catch (ArgumentException ex)
                    {
                        Logger.Exception(ex, $"线程Join操作参数异常，线程状态: {previewThread?.ThreadState}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Exception(ex, "停止预览线程时发生异常");
                    }
                }

                previewThread = null;
                previewCancellationTokenSource?.Dispose();
                previewCancellationTokenSource = null;
                previewPictureBox1 = null;
                previewPictureBox2 = null;

                Logger.Info("实时预览已停止");
            }
        }

        /// <summary>
        /// 预览工作线程
        /// </summary>
        private void PreviewWorker(CancellationToken cancellationToken)
        {
            Logger.Info("预览线程开始运行");

            while (!cancellationToken.IsCancellationRequested && previewThreadRunning)
            {
                try
                {
                    // 获取当前缓存的图像
                    IImage? image1 = null;
                    IImage? image2 = null;

                    lock (syncImageLock)
                    {
                        image1 = cachedImage1;
                        image2 = cachedImage2;
                    }

                    // 更新预览显示
                    if (image1 != null && previewPictureBox1 != null)
                    {
                        UpdatePictureBoxSafely(previewPictureBox1, image1);
                    }

                    if (image2 != null && previewPictureBox2 != null)
                    {
                        UpdatePictureBoxSafely(previewPictureBox2, image2);
                    }

                    // 控制预览更新频率，约10fps
                    Thread.Sleep(100);
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, "预览线程发生异常");
                    Thread.Sleep(500);
                }
            }

            Logger.Info("预览线程已停止");
        }



        /// <summary>
        /// 根据错误码获取详细错误信息
        /// </summary>
        private string GetErrorMessage(int errorCode)
        {
            switch (errorCode)
            {
                case MvError.MV_E_HANDLE: return "错误或无效句柄";
                case MvError.MV_E_SUPPORT: return "不支持的功能";
                case MvError.MV_E_BUFOVER: return "缓存已满";
                case MvError.MV_E_CALLORDER: return "函数调用顺序错误";
                case MvError.MV_E_PARAMETER: return "参数错误";
                case MvError.MV_E_RESOURCE: return "申请资源失败";
                case MvError.MV_E_NODATA: return "无数据（超时）";
                case MvError.MV_E_PRECONDITION: return "前置条件错误或运行环境改变";
                case MvError.MV_E_VERSION: return "版本不匹配";
                case MvError.MV_E_NOENOUGH_BUF: return "内存不足";
                case MvError.MV_E_UNKNOW: return "未知错误";
                case MvError.MV_E_GC_GENERIC: return "通用错误";
                case MvError.MV_E_GC_ACCESS: return "节点访问条件错误";
                case MvError.MV_E_ACCESS_DENIED: return "无权限";
                case MvError.MV_E_BUSY: return "设备忙碌或网络断开";
                case MvError.MV_E_NETER: return "网络错误";
                case unchecked((int)0x8000000D): return "没有可输出的缓存";
                case unchecked((int)0x8000000E): return "异常图像或数据格式错误";
                case unchecked((int)0x8000000F): return "图像数据不完整";
                case unchecked((int)0x80000010): return "设备断开连接";
                case unchecked((int)0x80000011): return "超时错误";
                case unchecked((int)0x80000012): return "内存分配失败";
                case unchecked((int)0x80000013): return "设备初始化失败";
                default: return $"未知错误码：{errorCode:X8}";
            }
        }

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        private bool ShouldRetry(int errorCode)
        {
            switch (errorCode)
            {
                case MvError.MV_E_NODATA: // 无数据（超时）
                case MvError.MV_E_BUFOVER: // 缓存已满
                case MvError.MV_E_NETER: // 网络错误
                case MvError.MV_E_BUSY: // 设备忙碌
                case unchecked((int)0x8000000D): // 没有可输出的缓存
                case unchecked((int)0x8000000E): // 异常图像或数据格式错误
                case unchecked((int)0x8000000F): // 图像数据不完整
                case unchecked((int)0x80000011): // 超时错误
                    return true;
                case MvError.MV_E_HANDLE: // 错误或无效句柄
                case MvError.MV_E_SUPPORT: // 不支持的功能
                case MvError.MV_E_PARAMETER: // 参数错误
                case MvError.MV_E_ACCESS_DENIED: // 无权限
                case unchecked((int)0x80000010): // 设备断开连接
                case unchecked((int)0x80000013): // 设备初始化失败
                    return false;
                default:
                    return true; // 默认重试
            }
        }

        public void Dispose()
        {
            Logger.Info("开始释放相机资源");

            try
            {
                // 停止预览线程
                StopLivePreview();

                // 停止同步缓存线程
                StopSynchronizedCacheThread();

                // 释放设备资源
                foreach (var device in devices)
                {
                    try
                    {
                        device?.StreamGrabber.StopGrabbing();
                        device?.Close();
                        device?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Logger.Exception(ex, "释放设备时发生异常");
                    }
                }

                devices.Clear();
                deviceInfos.Clear();

                // 释放缓存图像
                try
                {
                    cachedImage1?.Dispose();
                    cachedImage1 = null;
                    cachedImage2?.Dispose();
                    cachedImage2 = null;
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, "释放缓存图像时发生异常");
                }

                Logger.Info("相机资源释放完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "释放相机资源时发生异常");
            }
        }

        /// <summary>
        /// 获取设备状态信息
        /// </summary>
        public string GetDeviceStatus()
        {
            var status = new StringBuilder();
            status.AppendLine($"设备总数: {devices.Count}");
            status.AppendLine($"设备信息总数: {deviceInfos.Count}");
            status.AppendLine($"实时预览状态: {ShowLivePreview}");

            for (int i = 0; i < devices.Count; i++)
            {
                try
                {
                    var device = devices[i];
                    var deviceInfo = i < deviceInfos.Count ? deviceInfos[i] : null;

                    status.AppendLine($"设备 {i + 1}:");
                    status.AppendLine($"  序列号: {deviceInfo?.SerialNumber ?? "未知"}");
                    status.AppendLine($"  用户定义名称: {deviceInfo?.UserDefinedName ?? "未知"}");
                    status.AppendLine($"  设备对象: {(device != null ? "已创建" : "未创建")}");

                    if (device != null)
                    {
                        // 检查设备是否仍然连接
                        try
                        {
                            // 尝试读取一个简单的参数来检查连接状态
                            var result = device.Parameters.GetIntValue("Width", out IIntValue widthValue);
                            status.AppendLine($"  连接状态: {(result == 0 ? "已连接" : $"连接异常(错误码:{result:X8})")}");
                            if (result == 0 && widthValue != null)
                            {
                                status.AppendLine($"  图像宽度: {widthValue.ToString()}");
                            }
                        }
                        catch (Exception ex)
                        {
                            status.AppendLine($"  连接状态: 异常 - {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    status.AppendLine($"设备 {i + 1}: 获取状态时发生异常 - {ex.Message}");
                }
            }

            return status.ToString();
        }

        /// <summary>
        /// 重新连接设备
        /// </summary>
        public bool ReconnectDevice(int deviceIndex)
        {
            if (deviceIndex >= devices.Count || deviceIndex >= deviceInfos.Count)
            {
                Logger.Error($"设备索引超出范围：{deviceIndex}");
                return false;
            }

            try
            {
                Logger.Info($"尝试重新连接设备 {deviceIndex}");

                var deviceInfo = deviceInfos[deviceIndex];
                var oldDevice = devices[deviceIndex];

                // 关闭旧设备
                try
                {
                    oldDevice?.StreamGrabber.StopGrabbing();
                    oldDevice?.Close();
                    oldDevice?.Dispose();
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, $"关闭旧设备 {deviceIndex} 时发生异常");
                }

                // 创建新设备
                IDevice newDevice = DeviceFactory.CreateDevice(deviceInfo);
                if (newDevice == null)
                {
                    Logger.Error($"重新创建设备失败：{deviceInfo.SerialNumber}");
                    return false;
                }

                // 打开设备
                int nRet = newDevice.Open();
                if (nRet != 0)
                {
                    string errorMsg = GetErrorMessage(nRet);
                    Logger.Error($"重新打开设备失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                    newDevice.Dispose();
                    return false;
                }

                // 配置设备
                ConfigureDevice(newDevice, deviceInfo);

                // 开始采集
                newDevice.StreamGrabber.SetOutputQueueSize(3);
                newDevice.StreamGrabber.SetImageNodeNum(5);
                nRet = newDevice.StreamGrabber.StartGrabbing(StreamGrabStrategy.LatestImages);
                if (nRet != 0)
                {
                    string errorMsg = GetErrorMessage(nRet);
                    Logger.Error($"重新开始采集失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                    newDevice.Close();
                    newDevice.Dispose();
                    return false;
                }

                // 替换设备
                devices[deviceIndex] = newDevice;
                Logger.Info($"设备 {deviceIndex} 重新连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"重新连接设备 {deviceIndex} 时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 检查所有设备的健康状态
        /// </summary>
        public void CheckDeviceHealth()
        {
            Logger.Info("开始检查设备健康状态");

            for (int i = 0; i < devices.Count; i++)
            {
                try
                {
                    var device = devices[i];
                    if (device == null)
                    {
                        Logger.Warn($"设备 {i} 对象为空");
                        continue;
                    }

                    // 尝试获取设备参数来检查连接状态
                    var result = device.Parameters.GetIntValue("Width", out IIntValue widthValue);
                    if (result != 0)
                    {
                        Logger.Warn($"设备 {i} 连接异常，错误码：{result:X8}，尝试重新连接");
                        ReconnectDevice(i);
                    }
                    else
                    {
                        Logger.Debug($"设备 {i} 状态正常");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, $"检查设备 {i} 健康状态时发生异常");
                }
            }
        }


    }
}
