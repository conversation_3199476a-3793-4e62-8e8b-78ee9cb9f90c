
namespace GGEC.AOI.T68ZJ.Client
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            panel1 = new Panel();
            label12 = new Label();
            textBox4 = new TextBox();
            label8 = new Label();
            label10 = new Label();
            label7 = new Label();
            label4 = new Label();
            textBox3 = new TextBox();
            textBox2 = new TextBox();
            textBox1 = new TextBox();
            label6 = new Label();
            label5 = new Label();
            label3 = new Label();
            panel2 = new Panel();
            label15 = new Label();
            label1 = new Label();
            button3 = new Button();
            button2 = new Button();
            button1 = new Button();
            panel9 = new Panel();
            lb_jg = new Label();
            panel3 = new Panel();
            tb_lsjl = new Label();
            panelStatistics = new Panel();
            lblTotalCount = new Label();
            lblPassCount = new Label();
            lblPassRate = new Label();
            lblFailCount = new Label();
            lblFailRate = new Label();
            btnClearStats = new Button();
            lblTotalCountValue = new Label();
            lblPassCountValue = new Label();
            lblPassRateValue = new Label();
            lblFailCountValue = new Label();
            lblFailRateValue = new Label();
            bt_imgcheck = new Button();
            bt_video = new Button();
            chkEnableInference = new CheckBox();
            label14 = new Label();
            label13 = new Label();
            label9 = new Label();
            label2 = new Label();
            pictureBox4 = new PictureBox();
            pictureBox2 = new PictureBox();
            pictureBox1 = new PictureBox();
            panel5 = new Panel();
            panel6 = new Panel();
            panel7 = new Panel();
            pictureBox3 = new PictureBox();
            panel8 = new Panel();
            panel4 = new Panel();
            label11 = new Label();
            tb_lsjllist = new TextBox();
            panel1.SuspendLayout();
            panel2.SuspendLayout();
            panel9.SuspendLayout();
            panel3.SuspendLayout();
            panelStatistics.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox4).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            panel7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox3).BeginInit();
            panel4.SuspendLayout();
            SuspendLayout();
            // 
            // panel1
            // 
            panel1.BackColor = Color.FromArgb(63, 75, 97);
            panel1.Controls.Add(label12);
            panel1.Controls.Add(textBox4);
            panel1.Controls.Add(label8);
            panel1.Controls.Add(label10);
            panel1.Controls.Add(label7);
            panel1.Controls.Add(label4);
            panel1.Controls.Add(textBox3);
            panel1.Controls.Add(textBox2);
            panel1.Controls.Add(textBox1);
            panel1.Controls.Add(label6);
            panel1.Controls.Add(label5);
            panel1.Controls.Add(label3);
            panel1.Location = new Point(21, 37);
            panel1.Name = "panel1";
            panel1.Size = new Size(1874, 150);
            panel1.TabIndex = 0;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            label12.ForeColor = SystemColors.HighlightText;
            label12.Location = new Point(1322, 28);
            label12.Name = "label12";
            label12.Size = new Size(62, 25);
            label12.TabIndex = 104;
            label12.Text = "(机型)";
            // 
            // textBox4
            // 
            textBox4.Font = new Font("Microsoft YaHei UI", 12F);
            textBox4.Location = new Point(1244, 75);
            textBox4.Name = "textBox4";
            textBox4.ReadOnly = true;
            textBox4.Size = new Size(261, 38);
            textBox4.TabIndex = 109;
            textBox4.Text = "T68ZJ";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            label8.ForeColor = SystemColors.HighlightText;
            label8.Location = new Point(939, 25);
            label8.Name = "label8";
            label8.Size = new Size(98, 25);
            label8.TabIndex = 103;
            label8.Text = "(当前产线)";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label10.ForeColor = SystemColors.HighlightText;
            label10.Location = new Point(1244, 25);
            label10.Name = "label10";
            label10.Size = new Size(75, 31);
            label10.TabIndex = 108;
            label10.Text = "APN:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            label7.ForeColor = SystemColors.HighlightText;
            label7.Location = new Point(593, 28);
            label7.Name = "label7";
            label7.Size = new Size(98, 25);
            label7.TabIndex = 102;
            label7.Text = "(当前工站)";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            label4.ForeColor = SystemColors.HighlightText;
            label4.Location = new Point(157, 22);
            label4.Name = "label4";
            label4.Size = new Size(98, 25);
            label4.TabIndex = 101;
            label4.Text = "(当前用户)";
            // 
            // textBox3
            // 
            textBox3.Font = new Font("Microsoft YaHei UI", 12F);
            textBox3.Location = new Point(860, 75);
            textBox3.Name = "textBox3";
            textBox3.ReadOnly = true;
            textBox3.Size = new Size(283, 38);
            textBox3.TabIndex = 107;
            textBox3.Text = "J12W01";
            // 
            // textBox2
            // 
            textBox2.Font = new Font("Microsoft YaHei UI", 12F);
            textBox2.Location = new Point(461, 75);
            textBox2.Name = "textBox2";
            textBox2.ReadOnly = true;
            textBox2.Size = new Size(261, 38);
            textBox2.TabIndex = 106;
            textBox2.Text = "TestStation";
            // 
            // textBox1
            // 
            textBox1.Font = new Font("Microsoft YaHei UI", 12F);
            textBox1.Location = new Point(22, 75);
            textBox1.Name = "textBox1";
            textBox1.ReadOnly = true;
            textBox1.Size = new Size(313, 38);
            textBox1.TabIndex = 105;
            textBox1.Text = "361554";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label6.ForeColor = SystemColors.HighlightText;
            label6.Location = new Point(860, 22);
            label6.Name = "label6";
            label6.Size = new Size(71, 31);
            label6.TabIndex = 104;
            label6.Text = "Line:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label5.ForeColor = SystemColors.HighlightText;
            label5.Location = new Point(461, 22);
            label5.Name = "label5";
            label5.Size = new Size(135, 31);
            label5.TabIndex = 103;
            label5.Text = "StationID:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label3.ForeColor = SystemColors.HighlightText;
            label3.Location = new Point(22, 22);
            label3.Name = "label3";
            label3.Size = new Size(138, 31);
            label3.TabIndex = 102;
            label3.Text = "WorkerID:";
            // 
            // panel2
            // 
            panel2.BackColor = Color.FromArgb(63, 75, 97);
            panel2.Controls.Add(panelStatistics);
            panel2.Controls.Add(label15);
            panel2.Controls.Add(label1);
            panel2.Controls.Add(button3);
            panel2.Controls.Add(button2);
            panel2.Controls.Add(button1);
            panel2.Controls.Add(panel9);
            panel2.Controls.Add(panel3);
            panel2.Controls.Add(bt_imgcheck);
            panel2.Controls.Add(bt_video);
            panel2.Controls.Add(chkEnableInference);
            panel2.Controls.Add(label14);
            panel2.Controls.Add(label13);
            panel2.Controls.Add(label9);
            panel2.Controls.Add(label2);
            panel2.Controls.Add(pictureBox4);
            panel2.Controls.Add(pictureBox2);
            panel2.Controls.Add(pictureBox1);
            panel2.Controls.Add(panel5);
            panel2.Controls.Add(panel6);
            panel2.Controls.Add(panel7);
            panel2.Controls.Add(panel8);
            panel2.Location = new Point(21, 244);
            panel2.Name = "panel2";
            panel2.Size = new Size(1874, 1153);
            panel2.TabIndex = 1;
            // 
            // label15
            // 
            label15.BorderStyle = BorderStyle.FixedSingle;
            label15.Location = new Point(711, 101);
            label15.Name = "label15";
            label15.Size = new Size(1, 1000);
            label15.TabIndex = 163;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Microsoft YaHei UI", 18F, FontStyle.Bold);
            label1.ForeColor = SystemColors.HighlightText;
            label1.Location = new Point(1469, 342);
            label1.Name = "label1";
            label1.Size = new Size(102, 47);
            label1.TabIndex = 154;
            label1.Text = "结果:";
            // 
            // button3
            //
            button3.BackColor = SystemColors.MenuHighlight;
            button3.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            button3.ForeColor = SystemColors.Window;
            button3.Location = new Point(206, 578);
            button3.Name = "button3";
            button3.Size = new Size(68, 34);
            button3.TabIndex = 162;
            //button3.Text = "同拍";
            button3.UseVisualStyleBackColor = false;
            button3.Click += button3_Click;
            //
            // button2
            //
            button2.BackColor = SystemColors.MenuHighlight;
            button2.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            button2.ForeColor = SystemColors.Window;
            button2.Location = new Point(206, 33);
            button2.Name = "button2";
            button2.Size = new Size(68, 34);
            button2.TabIndex = 161;
            //button2.Text = "拍照2";
            button2.UseVisualStyleBackColor = false;
            button2.Click += button2_Click;
            // 
            // button1
            // 
            button1.BackColor = SystemColors.MenuHighlight;
            button1.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            button1.ForeColor = SystemColors.Window;
            button1.Location = new Point(1461, 256);
            button1.Name = "button1";
            button1.Size = new Size(335, 62);
            button1.TabIndex = 160;
            button1.Text = "视频测试";
            button1.UseVisualStyleBackColor = false;
            button1.Click += button1_Click;
            // 
            // panel9
            // 
            panel9.Controls.Add(lb_jg);
            panel9.Location = new Point(1461, 409);
            panel9.Name = "panel9";
            panel9.Size = new Size(335, 158);
            panel9.TabIndex = 159;
            // 
            // lb_jg
            // 
            lb_jg.AutoSize = true;
            lb_jg.Font = new Font("Microsoft YaHei UI", 40F, FontStyle.Bold);
            lb_jg.Location = new Point(80, 29);
            lb_jg.Name = "lb_jg";
            lb_jg.Size = new Size(0, 106);
            lb_jg.TabIndex = 155;
            // 
            // panel3
            // 
            panel3.BackColor = Color.FromArgb(77, 89, 115);
            panel3.Controls.Add(tb_lsjl);
            panel3.Font = new Font("Microsoft YaHei UI", 12F);
            panel3.ForeColor = SystemColors.HighlightText;
            panel3.Location = new Point(1441, 1120);
            panel3.Name = "panel3";
            panel3.Size = new Size(411, 200);
            panel3.TabIndex = 156;
            // 
            // tb_lsjl
            // 
            tb_lsjl.AutoSize = true;
            tb_lsjl.Location = new Point(19, 38);
            tb_lsjl.Name = "tb_lsjl";
            tb_lsjl.Size = new Size(0, 31);
            tb_lsjl.TabIndex = 0;
            // 
            // bt_imgcheck
            // 
            bt_imgcheck.BackColor = SystemColors.MenuHighlight;
            bt_imgcheck.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            bt_imgcheck.ForeColor = SystemColors.Window;
            bt_imgcheck.Location = new Point(1461, 157);
            bt_imgcheck.Name = "bt_imgcheck";
            bt_imgcheck.Size = new Size(335, 62);
            bt_imgcheck.TabIndex = 153;
            bt_imgcheck.Text = "手动校验";
            bt_imgcheck.UseVisualStyleBackColor = false;
            bt_imgcheck.Click += bt_imgcheck_Click;
            // 
            // bt_video
            // 
            bt_video.BackColor = SystemColors.MenuHighlight;
            bt_video.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            bt_video.ForeColor = SystemColors.Window;
            bt_video.Location = new Point(1461, 58);
            bt_video.Name = "bt_video";
            bt_video.Size = new Size(335, 62);
            bt_video.TabIndex = 152;
            bt_video.Text = "视频采集";
            bt_video.UseVisualStyleBackColor = false;
            bt_video.Click += bt_video_Click;
            //
            // chkEnableInference
            //
            chkEnableInference.AutoSize = true;
            chkEnableInference.Checked = true;
            chkEnableInference.CheckState = CheckState.Checked;
            chkEnableInference.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            chkEnableInference.ForeColor = SystemColors.HighlightText;
            chkEnableInference.Location = new Point(1461, 250);
            chkEnableInference.Name = "chkEnableInference";
            chkEnableInference.Size = new Size(158, 35);
            chkEnableInference.TabIndex = 154;
            chkEnableInference.Text = "启用AI推理";
            chkEnableInference.UseVisualStyleBackColor = true;
            chkEnableInference.CheckedChanged += chkEnableInference_CheckedChanged;
            //
            // label14
            // 
            label14.AutoSize = true;
            label14.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label14.ForeColor = SystemColors.HighlightText;
            label14.Location = new Point(746, 36);
            label14.Name = "label14";
            label14.Size = new Size(158, 31);
            label14.TabIndex = 151;
            label14.Text = "侧视检验结果";
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label13.ForeColor = SystemColors.HighlightText;
            label13.Location = new Point(746, 585);
            label13.Name = "label13";
            label13.Size = new Size(158, 31);
            label13.TabIndex = 150;
            label13.Text = "俯视检验结果";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label9.ForeColor = SystemColors.HighlightText;
            label9.Location = new Point(30, 36);
            label9.Name = "label9";
            label9.Size = new Size(144, 31);
            label9.TabIndex = 149;
            label9.Text = "视频流-侧视";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label2.ForeColor = SystemColors.HighlightText;
            label2.Location = new Point(26, 577);
            label2.Name = "label2";
            label2.Size = new Size(144, 31);
            label2.TabIndex = 148;
            label2.Text = "视频流-俯视";
            // 
            // pictureBox4
            // 
            pictureBox4.BackColor = Color.FromArgb(77, 89, 115);
            pictureBox4.Location = new Point(746, 627);
            pictureBox4.Name = "pictureBox4";
            pictureBox4.Size = new Size(640, 480);
            pictureBox4.TabIndex = 147;
            pictureBox4.TabStop = false;
            // 
            // pictureBox2
            // 
            pictureBox2.BackColor = Color.FromArgb(77, 89, 115);
            pictureBox2.Location = new Point(30, 627);
            pictureBox2.Name = "pictureBox2";
            pictureBox2.Size = new Size(640, 480);
            pictureBox2.TabIndex = 145;
            pictureBox2.TabStop = false;
            // 
            // pictureBox1
            // 
            pictureBox1.BackColor = Color.FromArgb(77, 89, 115);
            pictureBox1.Location = new Point(30, 87);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(640, 480);
            pictureBox1.TabIndex = 144;
            pictureBox1.TabStop = false;
            // 
            // panel5
            // 
            panel5.BackColor = SystemColors.Control;
            panel5.Location = new Point(30, 87);
            panel5.Name = "panel5";
            panel5.Size = new Size(640, 480);
            panel5.TabIndex = 157;
            // 
            // panel6
            // 
            panel6.BackColor = SystemColors.Control;
            panel6.Location = new Point(30, 627);
            panel6.Name = "panel6";
            panel6.Size = new Size(640, 480);
            panel6.TabIndex = 158;
            // 
            // panel7
            // 
            panel7.BackColor = SystemColors.Control;
            panel7.Controls.Add(pictureBox3);
            panel7.Location = new Point(746, 87);
            panel7.Name = "panel7";
            panel7.Size = new Size(640, 480);
            panel7.TabIndex = 158;
            // 
            // pictureBox3
            // 
            pictureBox3.BackColor = Color.FromArgb(77, 89, 115);
            pictureBox3.Location = new Point(0, 0);
            pictureBox3.Name = "pictureBox3";
            pictureBox3.Size = new Size(640, 480);
            pictureBox3.TabIndex = 146;
            pictureBox3.TabStop = false;
            // 
            // panel8
            // 
            panel8.BackColor = SystemColors.Control;
            panel8.Location = new Point(746, 627);
            panel8.Name = "panel8";
            panel8.Size = new Size(640, 480);
            panel8.TabIndex = 158;
            // 
            // panel4
            // 
            panel4.BackColor = Color.FromArgb(63, 75, 97);
            panel4.Controls.Add(label11);
            panel4.Controls.Add(tb_lsjllist);
            panel4.Location = new Point(1922, 37);
            panel4.Name = "panel4";
            panel4.Size = new Size(407, 1360);
            panel4.TabIndex = 2;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Font = new Font("Microsoft YaHei UI", 24F, FontStyle.Bold);
            label11.ForeColor = SystemColors.HighlightText;
            label11.Location = new Point(3, 28);
            label11.Name = "label11";
            label11.Size = new Size(391, 64);
            label11.TabIndex = 156;
            label11.Text = "Test Logs(记录)";
            // 
            // tb_lsjllist
            // 
            tb_lsjllist.BackColor = Color.FromArgb(77, 89, 115);
            tb_lsjllist.Font = new Font("Microsoft YaHei UI", 10F);
            tb_lsjllist.ForeColor = SystemColors.HighlightText;
            tb_lsjllist.Location = new Point(0, 141);
            tb_lsjllist.Multiline = true;
            tb_lsjllist.Name = "tb_lsjllist";
            tb_lsjllist.ReadOnly = true;
            tb_lsjllist.ScrollBars = ScrollBars.Vertical;
            tb_lsjllist.Size = new Size(404, 1219);
            tb_lsjllist.TabIndex = 139;
            //
            // panelStatistics
            //
            panelStatistics.BackColor = Color.FromArgb(77, 89, 115);
            panelStatistics.Controls.Add(lblTotalCount);
            panelStatistics.Controls.Add(lblTotalCountValue);
            panelStatistics.Controls.Add(lblPassCount);
            panelStatistics.Controls.Add(lblPassCountValue);
            panelStatistics.Controls.Add(lblPassRate);
            panelStatistics.Controls.Add(lblPassRateValue);
            panelStatistics.Controls.Add(lblFailCount);
            panelStatistics.Controls.Add(lblFailCountValue);
            panelStatistics.Controls.Add(lblFailRate);
            panelStatistics.Controls.Add(lblFailRateValue);
            panelStatistics.Controls.Add(btnClearStats);
            panelStatistics.Location = new Point(1461, 580);
            panelStatistics.Name = "panelStatistics";
            panelStatistics.Size = new Size(335, 527);
            panelStatistics.TabIndex = 164;
            //
            // lblTotalCount
            //
            lblTotalCount.AutoSize = true;
            lblTotalCount.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblTotalCount.ForeColor = SystemColors.HighlightText;
            lblTotalCount.Location = new Point(20, 20);
            lblTotalCount.Name = "lblTotalCount";
            lblTotalCount.Size = new Size(75, 31);
            lblTotalCount.TabIndex = 0;
            lblTotalCount.Text = "总数:";
            //
            // lblTotalCountValue
            //
            lblTotalCountValue.AutoSize = true;
            lblTotalCountValue.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblTotalCountValue.ForeColor = Color.White;
            lblTotalCountValue.Location = new Point(180, 20);
            lblTotalCountValue.Name = "lblTotalCountValue";
            lblTotalCountValue.Size = new Size(28, 31);
            lblTotalCountValue.TabIndex = 1;
            lblTotalCountValue.Text = "0";
            //
            // lblPassCount
            //
            lblPassCount.AutoSize = true;
            lblPassCount.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblPassCount.ForeColor = SystemColors.HighlightText;
            lblPassCount.Location = new Point(20, 70);
            lblPassCount.Name = "lblPassCount";
            lblPassCount.Size = new Size(95, 31);
            lblPassCount.TabIndex = 2;
            lblPassCount.Text = "合格数:";
            //
            // lblPassCountValue
            //
            lblPassCountValue.AutoSize = true;
            lblPassCountValue.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblPassCountValue.ForeColor = Color.LimeGreen;
            lblPassCountValue.Location = new Point(180, 70);
            lblPassCountValue.Name = "lblPassCountValue";
            lblPassCountValue.Size = new Size(28, 31);
            lblPassCountValue.TabIndex = 3;
            lblPassCountValue.Text = "0";
            //
            // lblPassRate
            //
            lblPassRate.AutoSize = true;
            lblPassRate.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblPassRate.ForeColor = SystemColors.HighlightText;
            lblPassRate.Location = new Point(20, 120);
            lblPassRate.Name = "lblPassRate";
            lblPassRate.Size = new Size(95, 31);
            lblPassRate.TabIndex = 4;
            lblPassRate.Text = "合格率:";
            //
            // lblPassRateValue
            //
            lblPassRateValue.AutoSize = true;
            lblPassRateValue.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblPassRateValue.ForeColor = Color.LimeGreen;
            lblPassRateValue.Location = new Point(180, 120);
            lblPassRateValue.Name = "lblPassRateValue";
            lblPassRateValue.Size = new Size(58, 31);
            lblPassRateValue.TabIndex = 5;
            lblPassRateValue.Text = "0.0%";
            //
            // lblFailCount
            //
            lblFailCount.AutoSize = true;
            lblFailCount.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblFailCount.ForeColor = SystemColors.HighlightText;
            lblFailCount.Location = new Point(20, 170);
            lblFailCount.Name = "lblFailCount";
            lblFailCount.Size = new Size(95, 31);
            lblFailCount.TabIndex = 6;
            lblFailCount.Text = "不良数:";
            //
            // lblFailCountValue
            //
            lblFailCountValue.AutoSize = true;
            lblFailCountValue.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblFailCountValue.ForeColor = Color.Red;
            lblFailCountValue.Location = new Point(180, 170);
            lblFailCountValue.Name = "lblFailCountValue";
            lblFailCountValue.Size = new Size(28, 31);
            lblFailCountValue.TabIndex = 7;
            lblFailCountValue.Text = "0";
            //
            // lblFailRate
            //
            lblFailRate.AutoSize = true;
            lblFailRate.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblFailRate.ForeColor = SystemColors.HighlightText;
            lblFailRate.Location = new Point(20, 220);
            lblFailRate.Name = "lblFailRate";
            lblFailRate.Size = new Size(95, 31);
            lblFailRate.TabIndex = 8;
            lblFailRate.Text = "不良率:";
            //
            // lblFailRateValue
            //
            lblFailRateValue.AutoSize = true;
            lblFailRateValue.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            lblFailRateValue.ForeColor = Color.Red;
            lblFailRateValue.Location = new Point(180, 220);
            lblFailRateValue.Name = "lblFailRateValue";
            lblFailRateValue.Size = new Size(58, 31);
            lblFailRateValue.TabIndex = 9;
            lblFailRateValue.Text = "0.0%";
            //
            // btnClearStats
            //
            btnClearStats.BackColor = Color.Orange;
            btnClearStats.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            btnClearStats.ForeColor = Color.White;
            btnClearStats.Location = new Point(20, 280);
            btnClearStats.Name = "btnClearStats";
            btnClearStats.Size = new Size(295, 50);
            btnClearStats.TabIndex = 10;
            btnClearStats.Text = "清零统计";
            btnClearStats.UseVisualStyleBackColor = false;
            //
            // MainForm
            //
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(58, 65, 75);
            ClientSize = new Size(2342, 1419);
            Controls.Add(panel4);
            Controls.Add(panel2);
            Controls.Add(panel1);
            Name = "MainForm";
            Text = "AI-AOI";
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            panel9.ResumeLayout(false);
            panel9.PerformLayout();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            panelStatistics.ResumeLayout(false);
            panelStatistics.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox4).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox2).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            panel7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBox3).EndInit();
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            ResumeLayout(false);
        }



        #endregion

        private Panel panel1;
        private TextBox textBox4;
        private Label label10;
        private TextBox textBox3;
        private TextBox textBox2;
        private TextBox textBox1;
        private Label label6;
        private Label label5;
        private Label label3;
        private Label label12;
        private Label label8;
        private Label label7;
        private Label label4;
        private Panel panel2;
        private Label label14;
        private Label label13;
        private Label label9;
        private Label label2;
        private PictureBox pictureBox4;
        private PictureBox pictureBox3;
        private PictureBox pictureBox2;
        private PictureBox pictureBox1;
        private Button bt_video;
        private Panel panel3;
        private Label lb_jg;
        private Label label1;
        private Panel panel4;
        private TextBox tb_lsjllist;
        private Label label11;
        private Button bt_imgcheck;
        private CheckBox chkEnableInference;
        private Panel panel5;
        private Panel panel6;
        private Panel panel7;
        private Panel panel8;
        private Label tb_lsjl;
        private Panel panel9;
        private Button button1;
        private Button button2;
        private Button button3;
        private Label label15;
        private Panel panelStatistics;
        private Label lblTotalCount;
        private Label lblPassCount;
        private Label lblPassRate;
        private Label lblFailCount;
        private Label lblFailRate;
        private Button btnClearStats;
        private Label lblTotalCountValue;
        private Label lblPassCountValue;
        private Label lblPassRateValue;
        private Label lblFailCountValue;
        private Label lblFailRateValue;
    }
}